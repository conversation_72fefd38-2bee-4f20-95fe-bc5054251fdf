using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Random = UnityEngine.Random;



public class JiqirenZhuozi : MonoBehaviour
{



    XYTablePanel xYTablePanel;
    XYPlayerPanel xYPlayerPanel;
    XYSetboboPanel xYSetboboPanel;

    private void Awake()
    {
        if (JiqirenCanshu.jihuo == false)
        {
            gameObject.SetActive(false);
        }
    }




    private enum Zhuangtai
    {
        空 = 0,
        等待中,
        准备,
        发牌,
        压注,
        组牌,
        结算,
        搓牌,
    }

    private enum Caozuo
    {
        空 = -1,
        丢 = 0,
        休,
        敲,
        大,
        拖,
        跟,
    }


    // Start is called before the first frame update
    IEnumerator Start()
    {
        yield return new WaitForSeconds(3f);

        xYTablePanel = GameObject.FindObjectOfType<XYTablePanel>();

        //循环 开局
        while (true)
        {
            //1等待中 2 准备 3 发牌 4压注 5组牌 6结算 7搓牌
            Debug.LogWarning("当前状态:" + (Zhuangtai)game.DataManager.instance.roominfo.roomdata.status);

            if (game.DataManager.instance.roominfo.roomdata.status == 1)
            {
                //1等待中
              
            }
            else if (game.DataManager.instance.roominfo.roomdata.status == 2)
            {
                //2 准备
               
            }
            else if (game.DataManager.instance.roominfo.roomdata.status == 3)
            {
                //3 发牌
                //yield return FapaiZT_SJ();
                yield return FapaiZT_LJ();
            }
            else if (game.DataManager.instance.roominfo.roomdata.status == 4)
            {
                //4压注
                yield return YazhuZT();
            }
            else if (game.DataManager.instance.roominfo.roomdata.status == 5)
            {
                //5组牌
                yield return ZupaiZT();
            }
            else if (game.DataManager.instance.roominfo.roomdata.status == 6)
            {
                //6结算

            }
            else if (game.DataManager.instance.roominfo.roomdata.status == 7)
            {
                //7搓牌

            }
            else
            {
                Debug.Log("未知对局状态 : "
                    + game.DataManager.instance.roominfo.roomdata.status
                    + "\t  -  1等待中 2 准备 3 发牌 4压注 5组牌 6结算 7搓牌");
            }


            
            yield return ZuoxiaXiangguan();

            yield return ZidongShangfen();

            yield return new WaitForSeconds(1f);
        }
         

    }

    private IEnumerator ZupaiZT()
    {
        
        yield return new WaitForSeconds(Random.Range(1, 2));

        //是否能操作
        var zupai = GameObject.FindObjectOfType<XYZupaiPanel>();
        //找到了,说明 激活了
        if (zupai != null)
        {
            
            //大牌分牌
            var dijiuwang = game.DataManager.instance.roominfo.roomdata.dijiuwang == 1;
            var cards = game.DataManager.instance.roominfo.userInfo.handCards.ToArray();

            if (cards.Length != 4)
            {
                yield break;
            }

            XYCardType tmpCardType = null;
            int tmpI = 0;
            int tmpJ = 1;

            //遍历找到最大的 组牌
            for (int i = 0; i < cards.Length - 1; i++)
            {
                for (int j = i + 1; j < cards.Length; j++)
                {
                    //判定  牌型
                    var type1 = XYAllCardType.checkCardType(new List<PaperCardStructrue>() {
                        cards[i],
                        cards[j]
                    }, dijiuwang);

                    // 对比大小
                    if (tmpCardType == null || (type1 != null && type1.value > tmpCardType.value))
                    {
                        tmpI = i;
                        tmpJ = j;
                        tmpCardType = type1;
                    }
                }
            }

            if (tmpCardType != null) 
            {
                Debug.LogWarning("分组找到最大的牌型:" + tmpCardType.typename);
                Debug.LogWarning("分组找到I:" + tmpI);
                Debug.LogWarning("分组找到J:" + tmpJ);

                zupai.clickBottomCard(tmpI);

                yield return new WaitForSeconds(5f);

                //因为 前面 点击 掉了 一张牌,所以 要 少1
                zupai.clickBottomCard(tmpJ-1);

                yield return new WaitForSeconds(Random.Range(1, 2));

                zupai.clickFenpai();

              
            }
           

            yield return new WaitForSeconds(Random.Range(1, 2));

    
       
 
        }
    }

    private int suijizhizhongzi = (int)DateTime.Now.Ticks;

    private IEnumerator FapaiZT_LJ()
    {
        yield return new WaitForSeconds(Random.Range(1,2));

        //丢 休 敲 大 拖 跟 
        var caozuowan = Caozuo.空;

        var dijiuwang = game.DataManager.instance.roominfo.roomdata.dijiuwang == 1;

        //是否能操作
        var option = GameObject.FindObjectOfType<XYOptionPanel>();
        //找到了,说明 激活了
        if (option != null)
        {
             
            //判定牌型
            var cards = game.DataManager.instance.roominfo.userInfo.handCards;

 
            if (cards.Count >= 2)
            {
                var cards1 = new List<PaperCardStructrue>
                {
                    cards[0],
                    cards[1]
                }; 

                var type1 = XYAllCardType.checkCardType(cards1, dijiuwang);
                 
                if (type1 != null)
                {
                    Debug.LogWarning("牌型:" + type1.typename);

                    //if (type1.typename == "2点")
                    //{
                    //    Debug.LogWarning("断点!");
                    //}

                    if (cards[0].num == 12 || cards[1].num == 12
                         || cards[0].num == 2 || cards[1].num == 2)
                    {
                        //有圈和2点这两张牌  随便配什么牌都大

                        if (option.daBtn.gameObject.activeInHierarchy)
                        {
                            option.suijiDa();

                            caozuowan = Caozuo.大;
                            Debug.Log("使用 大");
                        }
                        else
                        {
                            caozuowan = ShunxuCaozuo(option);
                        }
                    }
                    else
                    {
                        switch (type1.typename)
                        {
                            case "板凳":
                            case "豹子5":
                            case "长三":
                            case "豹子7":
                            case "豹子8":
                            case "豹子9":
                            case "梅十":
                            case "虎头":

                            case "和牌":
                            case "猫猫":
                            case "膏药":
                            case "人牌":
                            case "苕十":
                            case "天牌":
                            case "地牌":

                                //如果是对子  对子就敲 
                                if (option.qiaoBtn.gameObject.activeInHierarchy)
                                {
                                    option.clickQiao();

                                    caozuowan = Caozuo.敲;
                                    Debug.Log("使用 敲");
                                }
                                else
                                {
                                    caozuowan = ShunxuCaozuo(option);
                                }

                                break;

                            case "9点":
                                //比如刚开始两张杂牌加起来是9点   就跟   
                                if (option.genBtn.gameObject.activeInHierarchy)
                                {
                                    option.clickGen();

                                    caozuowan = Caozuo.跟;
                                    Debug.Log("使用 跟");
                                }
                                else
                                {
                                    caozuowan = ShunxuCaozuo(option);
                                }
                                break;
                            case "8点":
                            case "7点":
                            case "6点":
                            case "5点":
                            case "4点":
                            case "3点": 
                            case "2点":
                            case "1点":
                            case "0点":
                                //小于9点就丢  
                                if (option.diuBtn.gameObject.activeInHierarchy)
                                {
                                    option.clickDiu();

                                    caozuowan = Caozuo.丢;
                                    Debug.Log("使用 丢");
                                }
                                break;
                            default:

                                break;
                        }

                    }




                }
            }

            if (caozuowan == Caozuo.空)
            {
                if (option.diuBtn.gameObject.activeInHierarchy)
                {


                    option.clickDiu();

                    caozuowan = Caozuo.丢;
                    Debug.LogWarning("没有定义操作,则使用 丢");
                }

            }
            else
            {
                Debug.LogWarning("使用操作:"+caozuowan);
            }
            yield break;

            ////其他未知,则随机
            ////尝试100次
            //int jishuqi = 100;
            //while (jishuqi-- > 0)
            //{
                
            //    Random.InitState(suijizhizhongzi++);
            //    var sy = Random.Range(0, 6);

            //    switch (sy)
            //    {
            //        case 0:
            //            if (option.diuBtn.gameObject.activeInHierarchy)
            //            {
            //                option.clickDiu();

            //                caozuowan = Caozuo.丢;
            //                Debug.Log("使用 丢");
            //            }
            //            break;
            //        case 1:
            //            if (option.xiuBtn.gameObject.activeInHierarchy)
            //            {
            //                option.clickXiu();

            //                caozuowan = Caozuo.休;
            //                Debug.Log("使用 休");
            //            }
            //            break;
            //        case 2:
            //            if (option.qiaoBtn.gameObject.activeInHierarchy)
            //            {
            //                option.clickQiao();

            //                caozuowan = Caozuo.敲;
            //                Debug.Log("使用 敲");
            //            }
            //            break;
            //        case 3:
            //            if (option.daBtn.gameObject.activeInHierarchy)
            //            {
            //                option.suijiDa();

            //                caozuowan = Caozuo.大;
            //                Debug.Log("使用 大");
            //            }
            //            break;
            //        case 4:
            //            if (option.nodaBtn.gameObject.activeInHierarchy)
            //            {
            //                option.clickTuijian(0);

            //                caozuowan = Caozuo.拖;
            //                Debug.Log("使用 不大 ? 拖 ? 建议 ? ");
            //            }
            //            break;
            //        case 5:
            //            if (option.genBtn.gameObject.activeInHierarchy)
            //            {
            //                option.clickGen();

            //                caozuowan = Caozuo.跟;
            //                Debug.Log("使用 跟");
            //            }
            //            break; 
            //        default:
            //            break;
            //    }
                 
            //    yield break;

            //}

        }

    }

    private Caozuo ShunxuCaozuo(XYOptionPanel option)
    {
        if (option == null || option.gameObject.activeInHierarchy == false)
        {
            return Caozuo.空;
        }

        if (option.daBtn.gameObject.activeInHierarchy)
        {
            option.suijiDa();

            Debug.Log("使用 大");

            return Caozuo.大;
         
        }

        else if (option.genBtn.gameObject.activeInHierarchy)
        {
            option.clickGen();
            Debug.Log("使用 跟");
            return Caozuo.跟;
          
        }


        else if (option.qiaoBtn.gameObject.activeInHierarchy)
        {
            option.clickQiao();
            Debug.Log("使用 敲");
            return Caozuo.敲;

        }
        else
        {
            return Caozuo.空;
        }

     
    }

    private IEnumerator YazhuZT()
    {
        yield return new WaitForSeconds(Random.Range(1, 2));

        //丢 休 敲 大 拖 跟 
        var caozuowan = Caozuo.空;

        var dijiuwang = game.DataManager.instance.roominfo.roomdata.dijiuwang == 1;

        //是否能操作
        var option = GameObject.FindObjectOfType<XYOptionPanel>();
        //找到了,说明 激活了
        if (option != null)
        {

            //判定牌型
            var cards = game.DataManager.instance.roominfo.userInfo.handCards;


            if (cards.Count >= 2)
            {
                var cards1 = new List<PaperCardStructrue>
                {
                    cards[0],
                    cards[1]
                };

                var type1 = XYAllCardType.checkCardType(cards1, dijiuwang);

                if (type1 != null)
                {
                    Debug.LogWarning("牌型:" + type1.typename);


                    caozuowan = ShunxuCaozuo(option); 

                }
            }

            if (caozuowan == Caozuo.空)
            {
                if (option.diuBtn.gameObject.activeInHierarchy)
                {


                    option.clickDiu();

                    caozuowan = Caozuo.丢;
                    Debug.LogWarning("没有定义操作,则使用 丢");
                }

            }
            else
            {
                Debug.LogWarning("使用操作:" + caozuowan);
            }

            yield break;

             

        }

    }



    private IEnumerator FapaiZT_SJ()
    {
        yield return new WaitForSeconds(Random.Range(1, 2));

        //是否能操作
        var option = GameObject.FindObjectOfType<XYOptionPanel>();
        //找到了,说明 激活了
        if (option != null)
        {
            //丢 休 敲 大 拖 跟 
            bool caozuowan = false;

            //尝试100次
            int jishuqi = 100;
            while (jishuqi-- > 0)
            {

                Random.InitState(suijizhizhongzi++);
                var sy = Random.Range(0, 6);

                switch (sy)
                {
                    case 0:
                        if (option.diuBtn.gameObject.activeInHierarchy)
                        {
                            option.clickDiu();

                            caozuowan = true;
                            Debug.Log("使用 丢");
                        }
                        break;
                    case 1:
                        if (option.xiuBtn.gameObject.activeInHierarchy)
                        {
                            option.clickXiu();

                            caozuowan = true;
                            Debug.Log("使用 休");
                        }
                        break;
                    case 2:
                        if (option.qiaoBtn.gameObject.activeInHierarchy)
                        {
                            option.clickQiao();

                            caozuowan = true;
                            Debug.Log("使用 敲");
                        }
                        break;
                    case 3:
                        if (option.daBtn.gameObject.activeInHierarchy)
                        {
                            option.suijiDa();

                            caozuowan = true;
                            Debug.Log("使用 大");
                        }
                        break;
                    case 4:
                        if (option.nodaBtn.gameObject.activeInHierarchy)
                        {
                            option.clickTuijian(0);

                            caozuowan = true;
                            Debug.Log("使用 不大 ? 拖 ? 建议 ? ");
                        }
                        break;
                    case 5:
                        if (option.genBtn.gameObject.activeInHierarchy)
                        {
                            option.clickGen();

                            caozuowan = true;
                            Debug.Log("使用 跟");
                        }
                        break;
                    default:
                        break;
                }

                yield return new WaitForEndOfFrame();


                if (caozuowan)
                {
                    break;
                }
            }

        }

    }

    private IEnumerator ZuoxiaXiangguan()
    {
        yield return new WaitForEndOfFrame();

        //判定当前是否坐下
        if (game.DataManager.instance.roominfo.userInfo == null)
        {
            //选一个空位置坐下
            var sitItemList = GameObject.FindObjectsOfType<XYPlayerPanel>().ToList();
            sitItemList = sitItemList.Where(m => m.kongweiBtn.gameObject.activeSelf).ToList();

            if (sitItemList != null && sitItemList.Count > 0)
            {
                var wz = Random.Range(0, sitItemList.Count);
                var tmpZW = sitItemList[wz];
                tmpZW.clickKongwei();

                yield return new WaitForSeconds(3f);


            }
            else
            {
                Debug.LogError("没有空的座位,无法坐下!");

                yield break;
            }
        }
        else {

            xYPlayerPanel = xYTablePanel.players
                    .SingleOrDefault(m => m.playerid == game.DataManager.instance.m_UserInfo.id);

            if (xYPlayerPanel != null && xYPlayerPanel.huizhuoBtn.gameObject.activeInHierarchy)
            {
                //点击回到座位
                xYPlayerPanel.clickliuzuoHuizhuo();
            }
        }

        yield return new WaitForSeconds(1f);
        //else 
        //{
        //    //坐下后 , 上分1次
        //    //yield return ShangfenCaozuo();

        //    //判定是否 需要 回到座位
        //    //yield return Huidaozuowei();
        //}
    }

    private IEnumerator Huidaozuowei()
    {
        yield return new WaitForEndOfFrame();

        xYPlayerPanel = xYTablePanel.players
                       .SingleOrDefault(m => m.playerid == game.DataManager.instance.m_UserInfo.id);

        if (xYPlayerPanel!=null && xYPlayerPanel.huizhuoBtn.gameObject.activeInHierarchy)
        {
            //点击回到座位
            xYPlayerPanel.clickliuzuoHuizhuo();
        }

        yield return new WaitForSeconds(1f); 

    }


    private IEnumerator ZidongShangfen()
    {
        yield return new WaitForEndOfFrame ();

        if (game.DataManager.instance.roominfo.userInfo == null)
        {
            yield break;
        }
 

        if (game.DataManager.instance.roominfo.userInfo.clubIntegral <= 0)
        {
            Debug.LogError("俱乐部 积分不足了,无法上分!");
            yield break;
        }

        xYSetboboPanel = GameObject.FindObjectOfType<XYSetboboPanel>();

        if (xYSetboboPanel!=null)
        {
            xYSetboboPanel.SuijiXuanzeBobo();
            yield return new WaitForSeconds(1f);
            xYSetboboPanel.clickConfirm();
        }

        yield return new WaitForSeconds(1f);
    }


//private IEnumerator ShangfenCaozuo()
//{

//    int jishuqi = 10;
//    while (jishuqi-- > 0)
//    {
//        if (game.DataManager.instance.roominfo.userInfo.bobo > 0)
//        {
//            break;
//        }

//        if (game.DataManager.instance.roominfo.userInfo.clubIntegral <= 0)
//        {
//            Debug.LogError("俱乐部 积分不足了,无法上分!");
//            break;
//        }

//        yield return Shangfenyici();

//        yield return new WaitForSeconds(1f);

//    }

//}

//public IEnumerator Shangfenyici()
//{ 
//    //弹出菜单界面 
//    xYTablePanel.clickMenu();

//    yield return new WaitForSeconds(2f);

//    //模拟上分
//    if (xYSetboboPanel == null)
//    {
//        xYSetboboPanel = GameObject.FindObjectOfType<XYSetboboPanel>();
//    }

//    xYSetboboPanel?.clickConfirm();

//    yield return new WaitForSeconds(1f);
//}




// Update is called once per frame
void Update()
    {

    }
}
